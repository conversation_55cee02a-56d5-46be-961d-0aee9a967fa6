import {
  Body,
  Controller,
  Post,
  Put,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Batch, Run, RunUpdate } from './app.types';
import { AppService } from './app.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Post('/run')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        filters: { type: 'string' },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async run(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { filters: string },
  ) {
    const params: Run = {
      file: file,
      filters: body.filters,
    };
    return await this.appService.run(params);
  }

  @Put('/run')
  async updateRun(@Body() params: RunUpdate) {
    return await this.appService.update(params);
  }

  @Post('/batch')
  async batch(@Body() params: Batch) {
    return await this.appService.batch(params);
  }
}
