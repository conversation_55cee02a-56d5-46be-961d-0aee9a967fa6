import { Campaign, Campaigns, HotelPayload, RunMetrics } from 'src/app.types';
import {
  Price,
  Hotel,
  Company,
  HotelsPrices,
  HotelPrices,
  Inventory,
  CPAs,
  HotelCompany,
} from 'src/app.types';

export const getDate15DaysFromNow = (daysToSum: number): string => {
  const currentDate = new Date();

  currentDate.setDate(currentDate.getDate() + daysToSum);

  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  const day = String(currentDate.getDate()).padStart(2, '0');

  const formattedDate = `${year}${month}${day}`;

  return formattedDate;
};

export const aggregateHotelsPrices = (
  hotels: Hotel[],
  companies: Company[],
  prices: Price[],
) => {
  const hotelMap = {};
  hotels.forEach((hotel) => {
    hotelMap[hotel.id] = hotel.name;
  });

  const companyMap = {};
  companies.forEach((company) => {
    companyMap[company.id] = company.name;
  });

  const hotelsPrices: HotelsPrices = prices.reduce<HotelsPrices>(
    (acc, price) => {
      let hotelPrices: HotelPrices = acc.find(
        (hotel: HotelPrices) => hotel.trivagoId === price.hotelId,
      );

      if (!hotelPrices) {
        hotelPrices = {
          trivagoId: price.hotelId.toString(),
          hotelName: hotelMap[price.hotelId],
          prices: [],
        };
        acc.push(hotelPrices);
      }

      hotelPrices.prices.push({
        companyName: companyMap[price.companyId] ?? null,
        companyPrice: price.price,
      });

      return acc;
    },
    [],
  );

  return hotelsPrices;
};

export const aggregateHotels = (
  hotelsPrices: HotelPrices[],
  campaigns: Campaigns,
  inventory: Inventory,
  cpas: CPAs,
  run?: RunMetrics,
): HotelPayload[] => {
  const data: HotelPayload[] = [];

  hotelsPrices.forEach((hotel) => {
    if (!hasOurtripPrice(hotel.prices)) return;
    if (run) run.hotelsWithPrice += 1;

    const trivagoId = hotel.trivagoId;
    const hotelId = getHotelId(inventory, trivagoId);
    const hotelName = getHotelName(inventory, trivagoId);
    const minPrice = getOtherPrice(hotel.prices);
    const ourtripPrice = getOurtripPrice(hotel.prices);
    const cpa = getCPA(campaigns, cpas, hotelId);

    if (run && ourtripPrice == minPrice) {
      run.hotelsWithBestPrice += 1;
    }

    const price = Object({
      oth: hotelId,
      trivagoId: trivagoId,
      hotelName: hotelName,
      channel: 'TRIVAGO',
      currentPrice: ourtripPrice,
      otherPrice: minPrice,
      cpa,
    });

    // run?.destinations.forEach((destination) =>
    //   destination.pages.forEach((page) =>
    //     page.hotels.forEach((h) => {
    //       if (h.id.toString() === trivagoId.toString()) {
    //         h.hotelId = hotelId;
    //         h.trivagoId = trivagoId;
    //         h.currentPrice = ourtripPrice;
    //         h.otherPrice = minPrice;
    //         h.cpa = cpa;
    //       }
    //     }),
    //   ),
    // );

    data.push(price);
  });

  return data;
};

export const hasOurtripPrice = (prices: HotelCompany[]) => {
  return Boolean(prices.find((price) => price.companyName == 'Ourtrip'));
};

export const getHotelId = (inventory: Inventory, trivagoId: string) => {
  const inventoryHotel = inventory.find(
    (hotel) => hotel?.trivagoItemID?.toString() === trivagoId.toString(),
  );

  if (inventoryHotel) return inventoryHotel.partnerReference;

  return null;
};

export const getHotelName = (inventory: Inventory, trivagoId: string) => {
  const inventoryHotel = inventory.find(
    (hotel) => hotel?.trivagoItemID?.toString() === trivagoId.toString(),
  );

  if (inventoryHotel) return inventoryHotel.partnerHotelName;

  return null;
};

export const getOurtripPrice = (prices: HotelCompany[]) => {
  return prices.find((price: any) => price.companyName == 'Ourtrip')
    .companyPrice;
};

export const getCPA = (campaigns: Campaigns, cpas: CPAs, hotelId: string) => {
  const cpa = cpas.find(
    (hotel: any) =>
      hotel['locale'] === 'BR' && hotel['partner_reference'] == hotelId,
  );

  if (cpa) {
    const campaignCampaigns = cpa['campaign'];

    return (
      campaigns.find(
        (campaign: Campaign) => campaign['id'] == campaignCampaigns,
      )?.percentage ?? 0
    );
  }

  return null;
};

export const getOtherPrice = (companies: HotelCompany[]): number => {
  let min = companies[0].companyPrice;

  for (let i = 1; i < companies.length; i++) {
    if (
      companies[i].companyName != 'Ourtrip' &&
      companies[i].companyPrice < min
    ) {
      min = companies[i].companyPrice;
    }
  }

  return min;
};
