import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ScheduleModule } from '@nestjs/schedule';
import { AppSocket } from './app.socket';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { SupabaseModule } from 'nestjs-supabase-js';

@Module({
  imports: [
    ConfigModule.forRoot(),
    ScheduleModule.forRoot(),
    HttpModule.register({}),
    SupabaseModule.forRoot({
      supabaseKey: process.env.SUPABASE_KEY,
      supabaseUrl: process.env.SUPABASE_URL,
    }),
    EventEmitterModule.forRoot(),
    SupabaseModule.injectClient(),
  ],
  controllers: [AppController],
  providers: [AppService, AppSocket],
  exports: [AppModule],
})
export class AppModule {}
