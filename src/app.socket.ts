import { Logger } from '@nestjs/common';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Socket, Server } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { AppService } from './app.service';
import cities from './app.config';

@WebSocketGateway({ transports: ['websocket'] })
export class AppSocket
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  constructor(private readonly appService: AppService) {}

  private logger: Logger = new Logger(AppSocket.name);

  @WebSocketServer() socket: Server;

  afterInit() {
    this.logger.log('Initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client Connected: ${client.id}`);
    client.emit('init', {
      cities: cities,
      status: this.appService.getStatus(),
      runs: this.appService.getRuns(),
    });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client Disconnected: ${client.id}`);
  }

  @OnEvent('init')
  sendInitialData(initialData): void {
    this.socket.emit('init', initialData);
  }

  @OnEvent('run-metrics')
  sendRunMetrics(run): void {
    this.socket.emit('run-metrics', run);
  }

  @OnEvent('run-status')
  sendRunStatus(status: string): void {
    this.socket.emit('run-status', status);
  }
}
